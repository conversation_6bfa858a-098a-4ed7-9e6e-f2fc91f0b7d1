import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatGridListModule } from '@angular/material/grid-list';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatChipsModule } from '@angular/material/chips';
import { MatTableModule } from '@angular/material/table';
import { MatTabsModule } from '@angular/material/tabs';

import { ProductionService } from '../../core/services/production.service';
import { ProductService } from '../../core/services/product.service';
import { ProductionOrder, ProductionOrderStatus } from '../../core/models/product.model';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatGridListModule,
    MatProgressBarModule,
    MatChipsModule,
    MatTableModule,
    MatTabsModule
  ],
  template: `
    <div class="dashboard-container">
      <div class="dashboard-header">
        <h1>IFC Production Management Dashboard</h1>
        <p class="subtitle">Enhanced with Recursive Recipe Processing</p>
      </div>

      <!-- Key Metrics -->
      <div class="metrics-grid">
        <mat-card class="metric-card">
          <mat-card-header>
            <mat-card-title>
              <mat-icon color="primary">precision_manufacturing</mat-icon>
              Active Production Orders
            </mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="metric-value">{{ activeProductionOrders.length }}</div>
            <div class="metric-subtitle">Currently in progress</div>
          </mat-card-content>
          <mat-card-actions>
            <button mat-button routerLink="/production">View All</button>
          </mat-card-actions>
        </mat-card>

        <mat-card class="metric-card">
          <mat-card-header>
            <mat-card-title>
              <mat-icon color="accent">restaurant</mat-icon>
              Recipe Products
            </mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="metric-value">{{ recipeProductCount }}</div>
            <div class="metric-subtitle">With nested processing</div>
          </mat-card-content>
          <mat-card-actions>
            <button mat-button routerLink="/recipes">Manage Recipes</button>
          </mat-card-actions>
        </mat-card>

        <mat-card class="metric-card">
          <mat-card-header>
            <mat-card-title>
              <mat-icon color="warn">inventory</mat-icon>
              Low Stock Items
            </mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="metric-value">{{ lowStockCount }}</div>
            <div class="metric-subtitle">Need attention</div>
          </mat-card-content>
          <mat-card-actions>
            <button mat-button routerLink="/inventory">Check Inventory</button>
          </mat-card-actions>
        </mat-card>

        <mat-card class="metric-card">
          <mat-card-header>
            <mat-card-title>
              <mat-icon color="primary">check_circle</mat-icon>
              Completed Today
            </mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="metric-value">{{ completedTodayCount }}</div>
            <div class="metric-subtitle">Production orders</div>
          </mat-card-content>
          <mat-card-actions>
            <button mat-button routerLink="/reports/production">View Reports</button>
          </mat-card-actions>
        </mat-card>
      </div>

      <!-- Recent Production Orders -->
      <mat-card class="recent-orders-card">
        <mat-card-header>
          <mat-card-title>Recent Production Orders</mat-card-title>
          <mat-card-subtitle>Latest production activities with recursive processing status</mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <div class="table-container">
            <table mat-table [dataSource]="recentOrders" class="orders-table">
              <ng-container matColumnDef="orderNumber">
                <th mat-header-cell *matHeaderCellDef>Order #</th>
                <td mat-cell *matCellDef="let order">{{ order.orderNumber }}</td>
              </ng-container>

              <ng-container matColumnDef="product">
                <th mat-header-cell *matHeaderCellDef>Product</th>
                <td mat-cell *matCellDef="let order">
                  <div class="product-info">
                    <span class="product-code">{{ order.product?.productCode }}</span>
                    <span class="product-name">{{ order.product?.productName }}</span>
                  </div>
                </td>
              </ng-container>

              <ng-container matColumnDef="quantity">
                <th mat-header-cell *matHeaderCellDef>Quantity</th>
                <td mat-cell *matCellDef="let order">
                  {{ order.quantityToProduce }} {{ order.unit }}
                </td>
              </ng-container>

              <ng-container matColumnDef="status">
                <th mat-header-cell *matHeaderCellDef>Status</th>
                <td mat-cell *matCellDef="let order">
                  <mat-chip [color]="getStatusColor(order.status)">
                    {{ getStatusText(order.status) }}
                  </mat-chip>
                </td>
              </ng-container>

              <ng-container matColumnDef="hierarchy">
                <th mat-header-cell *matHeaderCellDef>Hierarchy</th>
                <td mat-cell *matCellDef="let order">
                  <div class="hierarchy-info">
                    <span class="level">Level {{ order.hierarchyLevel }}</span>
                    <mat-icon *ngIf="order.childProductionOrders.length > 0" 
                             matTooltip="Has sub-orders" 
                             color="accent">account_tree</mat-icon>
                  </div>
                </td>
              </ng-container>

              <ng-container matColumnDef="progress">
                <th mat-header-cell *matHeaderCellDef>Progress</th>
                <td mat-cell *matCellDef="let order">
                  <div class="progress-container">
                    <mat-progress-bar 
                      mode="determinate" 
                      [value]="order.completionPercentage">
                    </mat-progress-bar>
                    <span class="progress-text">{{ order.completionPercentage }}%</span>
                  </div>
                </td>
              </ng-container>

              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef>Actions</th>
                <td mat-cell *matCellDef="let order">
                  <button mat-icon-button 
                          [routerLink]="['/production', order.productionOrderId, 'view']"
                          matTooltip="View Details">
                    <mat-icon>visibility</mat-icon>
                  </button>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table>
          </div>
        </mat-card-content>
        <mat-card-actions>
          <button mat-raised-button color="primary" routerLink="/production/create">
            <mat-icon>add</mat-icon>
            Create New Order
          </button>
          <button mat-button routerLink="/production">View All Orders</button>
        </mat-card-actions>
      </mat-card>

      <!-- System Features -->
      <mat-card class="features-card">
        <mat-card-header>
          <mat-card-title>Enhanced System Features</mat-card-title>
          <mat-card-subtitle>New capabilities in the web-based system</mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <div class="features-grid">
            <div class="feature-item">
              <mat-icon color="primary">account_tree</mat-icon>
              <h3>Recursive Recipe Processing</h3>
              <p>Automatically handles nested recipes and sub-production orders</p>
            </div>
            
            <div class="feature-item">
              <mat-icon color="accent">security</mat-icon>
              <h3>Circular Dependency Detection</h3>
              <p>Prevents infinite loops in recipe hierarchies</p>
            </div>
            
            <div class="feature-item">
              <mat-icon color="primary">inventory_2</mat-icon>
              <h3>Multi-level Inventory Validation</h3>
              <p>Checks material availability across all recipe levels</p>
            </div>
            
            <div class="feature-item">
              <mat-icon color="accent">schedule</mat-icon>
              <h3>Automatic Production Sequencing</h3>
              <p>Orders sub-recipes before parent recipes automatically</p>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Quick Actions -->
      <div class="quick-actions">
        <h2>Quick Actions</h2>
        <div class="action-buttons">
          <button mat-raised-button color="primary" routerLink="/production/create">
            <mat-icon>add</mat-icon>
            Create Production Order
          </button>
          <button mat-raised-button color="accent" routerLink="/recipes/create">
            <mat-icon>restaurant</mat-icon>
            Create Recipe
          </button>
          <button mat-raised-button routerLink="/products/create">
            <mat-icon>inventory</mat-icon>
            Add Product
          </button>
          <button mat-raised-button routerLink="/inventory">
            <mat-icon>warehouse</mat-icon>
            Check Inventory
          </button>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .dashboard-container {
      padding: 20px;
      max-width: 1400px;
      margin: 0 auto;
    }

    .dashboard-header {
      text-align: center;
      margin-bottom: 30px;
    }

    .dashboard-header h1 {
      margin: 0;
      color: #1976d2;
      font-size: 2.5rem;
    }

    .subtitle {
      color: #666;
      font-size: 1.1rem;
      margin: 8px 0 0 0;
    }

    .metrics-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 20px;
      margin-bottom: 30px;
    }

    .metric-card {
      text-align: center;
    }

    .metric-card mat-card-title {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      font-size: 1rem;
    }

    .metric-value {
      font-size: 3rem;
      font-weight: bold;
      color: #1976d2;
      margin: 16px 0 8px 0;
    }

    .metric-subtitle {
      color: #666;
      font-size: 0.9rem;
    }

    .recent-orders-card {
      margin-bottom: 30px;
    }

    .table-container {
      overflow-x: auto;
      margin: 16px 0;
    }

    .orders-table {
      width: 100%;
      min-width: 800px;
    }

    .product-info {
      display: flex;
      flex-direction: column;
      gap: 4px;
    }

    .product-code {
      font-weight: 500;
      color: #1976d2;
    }

    .product-name {
      font-size: 0.9em;
      color: #666;
    }

    .hierarchy-info {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .level {
      font-size: 0.8em;
      background-color: #e3f2fd;
      padding: 2px 6px;
      border-radius: 12px;
      color: #1976d2;
    }

    .progress-container {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .progress-container mat-progress-bar {
      flex: 1;
      min-width: 80px;
    }

    .progress-text {
      font-size: 0.8em;
      color: #666;
      min-width: 35px;
    }

    .features-card {
      margin-bottom: 30px;
    }

    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
      margin-top: 16px;
    }

    .feature-item {
      text-align: center;
      padding: 16px;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      background-color: #fafafa;
    }

    .feature-item mat-icon {
      font-size: 2rem;
      width: 2rem;
      height: 2rem;
      margin-bottom: 8px;
    }

    .feature-item h3 {
      margin: 8px 0;
      color: #333;
      font-size: 1.1rem;
    }

    .feature-item p {
      margin: 0;
      color: #666;
      font-size: 0.9rem;
    }

    .quick-actions {
      text-align: center;
    }

    .quick-actions h2 {
      margin-bottom: 20px;
      color: #333;
    }

    .action-buttons {
      display: flex;
      justify-content: center;
      gap: 16px;
      flex-wrap: wrap;
    }

    .action-buttons button {
      min-width: 180px;
    }

    @media (max-width: 768px) {
      .metrics-grid {
        grid-template-columns: 1fr;
      }
      
      .features-grid {
        grid-template-columns: 1fr;
      }
      
      .action-buttons {
        flex-direction: column;
        align-items: center;
      }
    }
  `]
})
export class DashboardComponent implements OnInit {
  private productionService = inject(ProductionService);
  private productService = inject(ProductService);

  // Data properties
  activeProductionOrders: ProductionOrder[] = [];
  recentOrders: ProductionOrder[] = [];
  recipeProductCount = 0;
  lowStockCount = 0;
  completedTodayCount = 0;

  // Table configuration
  displayedColumns: string[] = ['orderNumber', 'product', 'quantity', 'status', 'hierarchy', 'progress', 'actions'];

  async ngOnInit() {
    await this.loadDashboardData();
  }

  private async loadDashboardData() {
    try {
      // Load active production orders
      this.activeProductionOrders = await this.productionService.getProductionOrdersByStatus(
        ProductionOrderStatus.InProgress
      );

      // Load recent orders (all statuses)
      const allOrders = await this.productionService.getProductionOrdersByStatus();
      this.recentOrders = allOrders
        .sort((a, b) => new Date(b.createdDate).getTime() - new Date(a.createdDate).getTime())
        .slice(0, 10);

      // Load recipe product count
      const recipeProducts = await this.productService.getRecipeProducts();
      this.recipeProductCount = recipeProducts.length;

      // Load low stock count
      const lowStockProducts = await this.productService.getLowStockProducts();
      this.lowStockCount = lowStockProducts.length;

      // Calculate completed today count
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      this.completedTodayCount = allOrders.filter(order => 
        order.status === ProductionOrderStatus.Completed &&
        order.actualEndDate &&
        new Date(order.actualEndDate) >= today
      ).length;

    } catch (error) {
      console.error('Error loading dashboard data:', error);
    }
  }

  getStatusColor(status: ProductionOrderStatus): string {
    switch (status) {
      case ProductionOrderStatus.Completed:
        return 'primary';
      case ProductionOrderStatus.InProgress:
        return 'accent';
      case ProductionOrderStatus.Cancelled:
      case ProductionOrderStatus.Failed:
        return 'warn';
      default:
        return '';
    }
  }

  getStatusText(status: ProductionOrderStatus): string {
    switch (status) {
      case ProductionOrderStatus.Planned:
        return 'Planned';
      case ProductionOrderStatus.MaterialReservation:
        return 'Reserving Materials';
      case ProductionOrderStatus.WaitingForDependencies:
        return 'Waiting for Dependencies';
      case ProductionOrderStatus.ReadyToStart:
        return 'Ready to Start';
      case ProductionOrderStatus.InProgress:
        return 'In Progress';
      case ProductionOrderStatus.Paused:
        return 'Paused';
      case ProductionOrderStatus.Completed:
        return 'Completed';
      case ProductionOrderStatus.Cancelled:
        return 'Cancelled';
      case ProductionOrderStatus.Failed:
        return 'Failed';
      case ProductionOrderStatus.OnHold:
        return 'On Hold';
      default:
        return 'Unknown';
    }
  }
}
