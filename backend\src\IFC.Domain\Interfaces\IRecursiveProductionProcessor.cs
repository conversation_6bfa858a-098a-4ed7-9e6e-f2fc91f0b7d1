using IFC.Domain.Entities;

namespace IFC.Domain.Interfaces;

/// <summary>
/// Interface for recursive production processing engine
/// </summary>
public interface IRecursiveProductionProcessor
{
    /// <summary>
    /// Processes a production order recursively, handling all nested recipes
    /// </summary>
    /// <param name="productId">Product to produce</param>
    /// <param name="quantity">Quantity to produce</param>
    /// <param name="costCenterId">Cost center for production</param>
    /// <param name="parentOrderId">Parent production order ID (null for top-level)</param>
    /// <param name="hierarchyLevel">Current hierarchy level (0 = top level)</param>
    /// <returns>Production order with all sub-orders created</returns>
    Task<ProductionOrder> ProcessRecursiveProductionAsync(
        int productId, 
        decimal quantity, 
        int costCenterId, 
        int? parentOrderId = null, 
        int hierarchyLevel = 0);
    
    /// <summary>
    /// Analyzes a recipe for nested dependencies and creates a production plan
    /// </summary>
    /// <param name="recipeId">Recipe to analyze</param>
    /// <param name="quantity">Quantity to produce</param>
    /// <returns>Production plan with dependency order</returns>
    Task<ProductionPlan> CreateProductionPlanAsync(int recipeId, decimal quantity);
    
    /// <summary>
    /// Validates that all dependencies can be satisfied for a production order
    /// </summary>
    /// <param name="productId">Product to validate</param>
    /// <param name="quantity">Quantity to validate</param>
    /// <param name="costCenterId">Cost center for production</param>
    /// <returns>Validation result with dependency analysis</returns>
    Task<DependencyValidationResult> ValidateDependenciesAsync(int productId, decimal quantity, int costCenterId);
    
    /// <summary>
    /// Calculates the total production time including all nested recipes
    /// </summary>
    /// <param name="productId">Product to calculate time for</param>
    /// <param name="quantity">Quantity to produce</param>
    /// <returns>Total production time in minutes</returns>
    Task<int> CalculateTotalProductionTimeAsync(int productId, decimal quantity);
    
    /// <summary>
    /// Gets the optimal production sequence for nested recipes
    /// </summary>
    /// <param name="productId">Product to sequence</param>
    /// <param name="quantity">Quantity to produce</param>
    /// <returns>Ordered list of production steps</returns>
    Task<IEnumerable<ProductionSequenceStep>> GetOptimalProductionSequenceAsync(int productId, decimal quantity);
    
    /// <summary>
    /// Resolves all material requirements recursively through the recipe hierarchy
    /// </summary>
    /// <param name="productId">Product to resolve materials for</param>
    /// <param name="quantity">Quantity to produce</param>
    /// <param name="resolvedMaterials">Dictionary to track resolved materials (prevents infinite loops)</param>
    /// <returns>Complete material requirements list</returns>
    Task<IEnumerable<MaterialRequirement>> ResolveMaterialRequirementsRecursivelyAsync(
        int productId, 
        decimal quantity, 
        Dictionary<int, decimal>? resolvedMaterials = null);
    
    /// <summary>
    /// Checks for circular dependencies in recipe hierarchy
    /// </summary>
    /// <param name="productId">Product to check</param>
    /// <param name="visitedProducts">Set of already visited products (for recursion tracking)</param>
    /// <returns>True if circular dependency exists</returns>
    Task<bool> HasCircularDependencyAsync(int productId, HashSet<int>? visitedProducts = null);
    
    /// <summary>
    /// Gets the maximum depth of recipe nesting for a product
    /// </summary>
    /// <param name="productId">Product to analyze</param>
    /// <returns>Maximum nesting depth</returns>
    Task<int> GetRecipeDepthAsync(int productId);
    
    /// <summary>
    /// Schedules production orders in dependency order
    /// </summary>
    /// <param name="productionOrders">List of production orders to schedule</param>
    /// <param name="startDate">Start date for scheduling</param>
    /// <returns>Scheduled production orders with dates</returns>
    Task<IEnumerable<ProductionOrder>> ScheduleProductionOrdersAsync(
        IEnumerable<ProductionOrder> productionOrders, 
        DateTime startDate);
}
