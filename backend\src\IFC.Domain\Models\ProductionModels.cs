using IFC.Domain.Entities;

namespace IFC.Domain.Models;

/// <summary>
/// Production validation result model
/// </summary>
public class ProductionValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public List<DependencyIssue> Issues { get; set; } = new();
    public List<MaterialIssue> MaterialIssues { get; set; } = new();
    public decimal EstimatedCost { get; set; }
    public int EstimatedDurationMinutes { get; set; }
}

/// <summary>
/// Dependency validation result model
/// </summary>
public class DependencyValidationResult
{
    public bool IsValid { get; set; }
    public List<DependencyIssue> Issues { get; set; } = new();
    public List<string> CircularDependencies { get; set; } = new();
    public int MaxDepthReached { get; set; }
}

/// <summary>
/// Dependency issue model
/// </summary>
public class DependencyIssue
{
    public int ProductId { get; set; }
    public string ProductCode { get; set; } = string.Empty;
    public string ProductName { get; set; } = string.Empty;
    public string IssueType { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int HierarchyLevel { get; set; }
}

/// <summary>
/// Material issue model
/// </summary>
public class MaterialIssue
{
    public int ProductId { get; set; }
    public string ProductCode { get; set; } = string.Empty;
    public string ProductName { get; set; } = string.Empty;
    public decimal RequiredQuantity { get; set; }
    public decimal AvailableQuantity { get; set; }
    public decimal ShortageQuantity { get; set; }
    public string Unit { get; set; } = string.Empty;
    public int CostCenterId { get; set; }
}

/// <summary>
/// Production plan model
/// </summary>
public class ProductionPlan
{
    public int ProductionPlanId { get; set; }
    public string PlanName { get; set; } = string.Empty;
    public DateTime PlannedStartDate { get; set; }
    public DateTime PlannedEndDate { get; set; }
    public List<ProductionPlanStep> Steps { get; set; } = new();
    public decimal TotalEstimatedCost { get; set; }
    public int TotalEstimatedTimeMinutes { get; set; }
    public string Status { get; set; } = string.Empty;
}

/// <summary>
/// Production plan step model
/// </summary>
public class ProductionPlanStep
{
    public int StepNumber { get; set; }
    public int ProductId { get; set; }
    public string ProductCode { get; set; } = string.Empty;
    public string ProductName { get; set; } = string.Empty;
    public decimal Quantity { get; set; }
    public string Unit { get; set; } = string.Empty;
    public int HierarchyLevel { get; set; }
    public DateTime PlannedStartTime { get; set; }
    public DateTime PlannedEndTime { get; set; }
    public List<int> Prerequisites { get; set; } = new();
    public decimal EstimatedCost { get; set; }
    public int EstimatedTimeMinutes { get; set; }
}

/// <summary>
/// Production sequence step model
/// </summary>
public class ProductionSequenceStep
{
    public int StepNumber { get; set; }
    public int ProductId { get; set; }
    public string ProductCode { get; set; } = string.Empty;
    public string ProductName { get; set; } = string.Empty;
    public decimal Quantity { get; set; }
    public string Unit { get; set; } = string.Empty;
    public int HierarchyLevel { get; set; }
    public DateTime ScheduledStartTime { get; set; }
    public DateTime ScheduledEndTime { get; set; }
    public List<int> DependsOnSteps { get; set; } = new();
    public string Status { get; set; } = string.Empty;
}

/// <summary>
/// Production hierarchy model
/// </summary>
public class ProductionHierarchy
{
    public int ProductId { get; set; }
    public string ProductCode { get; set; } = string.Empty;
    public string ProductName { get; set; } = string.Empty;
    public decimal Quantity { get; set; }
    public string Unit { get; set; } = string.Empty;
    public int HierarchyLevel { get; set; }
    public bool IsRawMaterial { get; set; }
    public bool RequiresProduction { get; set; }
    public List<ProductionHierarchy> Children { get; set; } = new();
    public decimal EstimatedCost { get; set; }
    public int EstimatedTimeMinutes { get; set; }
}

/// <summary>
/// Material requirements model
/// </summary>
public class MaterialRequirements
{
    public int ProductId { get; set; }
    public string ProductCode { get; set; } = string.Empty;
    public string ProductName { get; set; } = string.Empty;
    public List<MaterialRequirement> Requirements { get; set; } = new();
    public decimal TotalEstimatedCost { get; set; }
}

/// <summary>
/// Material requirement model
/// </summary>
public class MaterialRequirement
{
    public int ProductId { get; set; }
    public string ProductCode { get; set; } = string.Empty;
    public string ProductName { get; set; } = string.Empty;
    public decimal RequiredQuantity { get; set; }
    public string Unit { get; set; } = string.Empty;
    public bool IsRawMaterial { get; set; }
    public bool RequiresProduction { get; set; }
    public int HierarchyLevel { get; set; }
    public decimal UnitCost { get; set; }
    public decimal TotalCost { get; set; }
}

/// <summary>
/// Material reservation result model
/// </summary>
public class MaterialReservationResult
{
    public bool IsSuccessful { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<MaterialReservation> Reservations { get; set; } = new();
    public decimal TotalReservedValue { get; set; }
}

/// <summary>
/// Material reservation model
/// </summary>
public class MaterialReservation
{
    public int ProductId { get; set; }
    public string ProductCode { get; set; } = string.Empty;
    public decimal ReservedQuantity { get; set; }
    public string Unit { get; set; } = string.Empty;
    public int CostCenterId { get; set; }
    public DateTime ReservationDate { get; set; }
    public string ReservationReference { get; set; } = string.Empty;
}
