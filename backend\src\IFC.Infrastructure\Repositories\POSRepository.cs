using Microsoft.EntityFrameworkCore;
using IFC.Domain.Entities.POS;
using IFC.Application.Interfaces;
using IFC.Infrastructure.Data;

namespace IFC.Infrastructure.Repositories;

/// <summary>
/// Repository implementation for POS operations
/// Replicates VB.NET POS database operations exactly
/// </summary>
public class POSRepository : IPOSRepository
{
    private readonly POSDbContext _context;

    public POSRepository(POSDbContext context)
    {
        _context = context;
    }

    public async Task<IEnumerable<POSSetting>> GetPOSSettingsAsync()
    {
        // Replicates VB.NET: "Select * from POSSetting Order By Company_NamePOS"
        return await _context.POSSettings
            .Where(p => p.IsActive)
            .OrderBy(p => p.CompanyNamePOS)
            .ToListAsync();
    }

    public async Task<IEnumerable<POSSetting>> GetPOSSettingsByCostCenterAsync(int costCenterIdPOS)
    {
        // Replicates VB.NET: "select * from POSSetting Where CostCenter_IdPOS=" & CostCenter_IdPOS
        return await _context.POSSettings
            .Where(p => p.CostCenterIdPOS == costCenterIdPOS && p.IsActive)
            .ToListAsync();
    }

    public async Task<POSSetting?> GetPOSSettingAsync(int companyId, int outletId, int costCenterId)
    {
        // Replicates VB.NET: GetSettingPOS function
        return await _context.POSSettings
            .FirstOrDefaultAsync(p => 
                p.CompanyIdPOS == companyId && 
                p.BrandIdPOS == outletId && 
                p.CostCenterIdPOS == costCenterId &&
                p.IsActive);
    }

    public async Task<IEnumerable<POSTransaction>> LoadPOSDataAsync(DateTime fromDate, DateTime toDate, int costCenterIdPOS)
    {
        // Replicates VB.NET: LoadPosData function
        var fromDateFormatted = fromDate.Date;
        var toDateFormatted = toDate.Date.AddDays(1).AddTicks(-1);

        // Get POS settings for this cost center
        var posSettings = await GetPOSSettingsByCostCenterAsync(costCenterIdPOS);
        var companyIds = posSettings.Select(p => p.CompanyIdPOS).ToList();
        var outletIds = posSettings.Select(p => p.BrandIdPOS).ToList();
        var centerIds = posSettings.Select(p => p.CostCenterIdPOS).ToList();

        return await _context.POSTransactions
            .Where(t => companyIds.Contains(t.Mandant) &&
                       outletIds.Contains(t.Outlet) &&
                       centerIds.Contains(t.Center) &&
                       t.StatistDate >= fromDateFormatted &&
                       t.StatistDate <= toDateFormatted)
            .OrderBy(t => t.Mandant)
            .ThenBy(t => t.Outlet)
            .ThenBy(t => t.Center)
            .ThenBy(t => t.PLU)
            .ToListAsync();
    }

    public async Task<IEnumerable<POSTransaction>> LoadPOSDataOnlineAsync(DateTime fromDate, DateTime toDate)
    {
        // Replicates VB.NET: LoadPosDataOnLine function
        return await _context.POSTransactions
            .Where(t => t.IsIFCDone == 0) // Only unprocessed transactions
            .OrderBy(t => t.Mandant)
            .ThenBy(t => t.Outlet)
            .ThenBy(t => t.Center)
            .ThenBy(t => t.PLU)
            .ToListAsync();
    }

    public async Task<IEnumerable<POSTransaction>> GetUnprocessedTransactionsAsync()
    {
        return await _context.POSTransactions
            .Where(t => t.IsIFCDone == 0)
            .ToListAsync();
    }

    public async Task<bool> UpdateTransactionAsProcessedAsync(int chDID)
    {
        // Replicates VB.NET: UpdateCHDIDOnline function
        // Note: This would typically update the underlying table, not the view
        try
        {
            var sql = "UPDATE Check_Details SET IsIFCDone=1 WHERE ChDID=@chDID";
            await _context.Database.ExecuteSqlRawAsync(sql, chDID);
            return true;
        }
        catch
        {
            return false;
        }
    }

    public async Task<bool> ExecutePOSCommandAsync(string sqlCommand)
    {
        // Replicates VB.NET: EXECUT_TxtPOS function
        try
        {
            await _context.Database.ExecuteSqlRawAsync(sqlCommand);
            return true;
        }
        catch
        {
            return false;
        }
    }

    public async Task<IEnumerable<T>> ExecutePOSQueryAsync<T>(string sqlQuery) where T : class
    {
        // Replicates VB.NET: SELECT_TXTPOS function
        // Note: This is a simplified version - in practice you'd need to map the results properly
        try
        {
            return await _context.Set<T>().FromSqlRaw(sqlQuery).ToListAsync();
        }
        catch
        {
            return new List<T>();
        }
    }

    public async Task<bool> EnsureViewExistsAsync()
    {
        // Replicates VB.NET: CheckExistV_ProcIFC function
        try
        {
            var checkSql = "SELECT * FROM sys.objects WHERE object_id=OBJECT_ID('v_ProcIFC')";
            var exists = await _context.Database.SqlQueryRaw<int>($"SELECT COUNT(*) FROM ({checkSql}) AS t").FirstOrDefaultAsync();
            
            if (exists == 0)
            {
                // Create the view - replicates VB.NET view creation
                var createViewSql = @"
                    CREATE VIEW [dbo].[v_ProcIFC]
                    AS
                    SELECT CCGCode AS mandant, CCCode AS outlet, FloorID AS center, FloorName AS centername, 
                           ArtCode AS plu, ArtName AS article, SUM(NetQty) AS Qty, AVG(UnitPrice) AS price, 
                           SUM(NetPrice) AS Amount, SUM(DeducAmount) AS discount, 
                           SUM(ChkTaxesAmount_1 + ChkTaxesAmount_2) AS taxamount, SysDate AS statistdate, 
                           SUM(CASE (dbo.vOrders.IsIFCDone) WHEN 1 THEN 1 ELSE 0 END) AS IsIFCDone, 
                           0 AS ChDID
                    FROM dbo.vOrders
                    GROUP BY CCGCode, CCCode, FloorID, FloorName, ArtCode, ArtName, SysDate";
                
                await _context.Database.ExecuteSqlRawAsync(createViewSql);
            }
            
            return true;
        }
        catch
        {
            return false;
        }
    }

    public async Task<bool> DropViewIfExistsAsync()
    {
        // Replicates VB.NET: DeleteViewIFC function
        try
        {
            var checkSql = "SELECT * FROM sys.Views WHERE name ='v_ProcIFC'";
            var exists = await _context.Database.SqlQueryRaw<int>($"SELECT COUNT(*) FROM ({checkSql}) AS t").FirstOrDefaultAsync();
            
            if (exists > 0)
            {
                await _context.Database.ExecuteSqlRawAsync("DROP VIEW v_ProcIFC");
            }
            
            return true;
        }
        catch
        {
            return false;
        }
    }

    public async Task<SalesPOS> InsertSalesPOSAsync(SalesPOS salesPOS)
    {
        _context.SalesPOS.Add(salesPOS);
        await _context.SaveChangesAsync();
        return salesPOS;
    }

    public async Task<IEnumerable<SalesPOS>> GetSalesDataAsync(DateTime fromDate, DateTime toDate, int? costCenterId = null)
    {
        var query = _context.SalesPOS
            .Where(s => s.TransactionDateCreate >= fromDate && s.TransactionDateCreate <= toDate);

        if (costCenterId.HasValue)
        {
            query = query.Where(s => s.CostCenterPOSId == costCenterId.Value);
        }

        return await query
            .OrderBy(s => s.TransactionDateCreate)
            .ToListAsync();
    }

    public async Task<bool> TestConnectionAsync()
    {
        // Replicates VB.NET: TestConnectionPOS function
        try
        {
            await _context.Database.OpenConnectionAsync();
            await _context.Database.CloseConnectionAsync();
            return true;
        }
        catch
        {
            return false;
        }
    }
}
