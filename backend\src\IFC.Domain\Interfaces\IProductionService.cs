using IFC.Domain.Entities;
using IFC.Domain.Models;
using IFC.Domain.Enums;

namespace IFC.Domain.Interfaces;

/// <summary>
/// Interface for production processing services
/// </summary>
public interface IProductionService
{
    /// <summary>
    /// Creates a production order with recursive processing for nested recipes
    /// </summary>
    /// <param name="productId">Product to produce</param>
    /// <param name="quantity">Quantity to produce</param>
    /// <param name="costCenterId">Cost center for production</param>
    /// <param name="plannedStartDate">Planned start date</param>
    /// <param name="priority">Production priority</param>
    /// <returns>Created production order with all sub-orders</returns>
    Task<ProductionOrder> CreateProductionOrderAsync(
        int productId,
        decimal quantity,
        int costCenterId,
        DateTime plannedStartDate,
        int priority = 5);

    /// <summary>
    /// Validates if a production order can be executed (checks inventory, dependencies, etc.)
    /// </summary>
    /// <param name="productionOrderId">Production order to validate</param>
    /// <returns>Validation result with details</returns>
    Task<ProductionValidationResult> ValidateProductionOrderAsync(int productionOrderId);

    /// <summary>
    /// Starts production for a production order
    /// </summary>
    /// <param name="productionOrderId">Production order to start</param>
    /// <param name="userId">User starting the production</param>
    /// <returns>Updated production order</returns>
    Task<ProductionOrder> StartProductionAsync(int productionOrderId, int userId);

    /// <summary>
    /// Completes production for a production order
    /// </summary>
    /// <param name="productionOrderId">Production order to complete</param>
    /// <param name="actualQuantityProduced">Actual quantity produced</param>
    /// <param name="userId">User completing the production</param>
    /// <returns>Updated production order</returns>
    Task<ProductionOrder> CompleteProductionAsync(int productionOrderId, decimal actualQuantityProduced, int userId);

    /// <summary>
    /// Cancels a production order and releases reserved materials
    /// </summary>
    /// <param name="productionOrderId">Production order to cancel</param>
    /// <param name="reason">Reason for cancellation</param>
    /// <param name="userId">User cancelling the production</param>
    /// <returns>Updated production order</returns>
    Task<ProductionOrder> CancelProductionAsync(int productionOrderId, string reason, int userId);

    /// <summary>
    /// Gets the production hierarchy for a product (all nested recipes)
    /// </summary>
    /// <param name="productId">Product to analyze</param>
    /// <param name="quantity">Quantity to produce</param>
    /// <returns>Production hierarchy tree</returns>
    Task<ProductionHierarchy> GetProductionHierarchyAsync(int productId, decimal quantity);

    /// <summary>
    /// Calculates material requirements for a production order including all nested recipes
    /// </summary>
    /// <param name="productId">Product to produce</param>
    /// <param name="quantity">Quantity to produce</param>
    /// <returns>Material requirements breakdown</returns>
    Task<MaterialRequirements> CalculateMaterialRequirementsAsync(int productId, decimal quantity);

    /// <summary>
    /// Reserves materials for a production order
    /// </summary>
    /// <param name="productionOrderId">Production order to reserve materials for</param>
    /// <returns>Reservation result</returns>
    Task<MaterialReservationResult> ReserveMaterialsAsync(int productionOrderId);

    /// <summary>
    /// Releases reserved materials for a production order
    /// </summary>
    /// <param name="productionOrderId">Production order to release materials for</param>
    /// <returns>Release result</returns>
    Task<MaterialReservationResult> ReleaseMaterialsAsync(int productionOrderId);

    /// <summary>
    /// Records material consumption for a production step
    /// </summary>
    /// <param name="productionOrderId">Production order</param>
    /// <param name="recipeIngredientId">Recipe ingredient being consumed</param>
    /// <param name="actualQuantity">Actual quantity consumed</param>
    /// <param name="batchId">Batch ID if applicable</param>
    /// <param name="userId">User recording the consumption</param>
    /// <returns>Material consumption record</returns>
    Task<MaterialConsumption> RecordMaterialConsumptionAsync(
        int productionOrderId,
        int recipeIngredientId,
        decimal actualQuantity,
        int? batchId,
        int userId);

    /// <summary>
    /// Gets production orders by status
    /// </summary>
    /// <param name="status">Status to filter by</param>
    /// <param name="costCenterId">Optional cost center filter</param>
    /// <returns>List of production orders</returns>
    Task<IEnumerable<ProductionOrder>> GetProductionOrdersByStatusAsync(
        ProductionOrderStatus status,
        int? costCenterId = null);

    /// <summary>
    /// Gets production order with full hierarchy
    /// </summary>
    /// <param name="productionOrderId">Production order ID</param>
    /// <returns>Production order with all related data</returns>
    Task<ProductionOrder?> GetProductionOrderWithHierarchyAsync(int productionOrderId);
}
