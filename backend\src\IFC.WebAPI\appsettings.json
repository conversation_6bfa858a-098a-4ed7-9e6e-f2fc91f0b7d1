{"ConnectionStrings": {"SCMConnection": "Data Source=localhost;Initial Catalog=TibaRosee;Persist Security Info=True;MultipleActiveResultSets=True;User ID=sa;Password=*******;TrustServerCertificate=true", "POSConnection": "Data Source=localhost;Initial Catalog=Tiba_Pos;Persist Security Info=True;MultipleActiveResultSets=True;User ID=sa;Password=*******;TrustServerCertificate=true"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Information"}}, "AllowedHosts": "*", "Cors": {"AllowedOrigins": ["http://localhost:4200", "https://localhost:4200"]}, "IFC": {"SystemName": "IFC Production Management System", "Version": "2.0.0", "DatabaseName": "TibaRosee", "EnableRecursiveProcessing": true, "MaxRecursionDepth": 10, "DefaultCostCenterId": 1}}