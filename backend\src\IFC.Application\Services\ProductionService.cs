using IFC.Domain.Entities;
using IFC.Domain.Enums;
using IFC.Domain.Interfaces;
using IFC.Domain.Models;
using IFC.Application.Interfaces;
using Microsoft.Extensions.Logging;

namespace IFC.Application.Services;

/// <summary>
/// Main production service that orchestrates production operations
/// </summary>
public class ProductionService : IProductionService
{
    private readonly ILogger<ProductionService> _logger;
    private readonly IRecursiveProductionProcessor _recursiveProcessor;
    private readonly IProductRepository _productRepository;
    private readonly IRecipeRepository _recipeRepository;
    private readonly IInventoryRepository _inventoryRepository;
    private readonly IProductionOrderRepository _productionOrderRepository;

    public ProductionService(
        ILogger<ProductionService> logger,
        IRecursiveProductionProcessor recursiveProcessor,
        IProductRepository productRepository,
        IRecipeRepository recipeRepository,
        IInventoryRepository inventoryRepository,
        IProductionOrderRepository productionOrderRepository)
    {
        _logger = logger;
        _recursiveProcessor = recursiveProcessor;
        _productRepository = productRepository;
        _recipeRepository = recipeRepository;
        _inventoryRepository = inventoryRepository;
        _productionOrderRepository = productionOrderRepository;
    }

    public async Task<ProductionOrder> CreateProductionOrderAsync(
        int productId, 
        decimal quantity, 
        int costCenterId, 
        DateTime plannedStartDate, 
        int priority = 5)
    {
        _logger.LogInformation("Creating production order for Product {ProductId}, Quantity {Quantity}", productId, quantity);

        // Validate inputs
        var product = await _productRepository.GetByIdAsync(productId);
        if (product == null)
            throw new ArgumentException($"Product with ID {productId} not found");

        if (!product.IsProduction && !product.IsRecipe)
            throw new InvalidOperationException($"Product {product.ProductCode} is not a production or recipe item");

        // Validate dependencies and materials
        var validationResult = await ValidateProductionOrderAsync(productId, quantity, costCenterId);
        if (!validationResult.IsValid)
        {
            var errors = string.Join("; ", validationResult.Errors);
            throw new InvalidOperationException($"Production order validation failed: {errors}");
        }

        // Use recursive processor to create the production order hierarchy
        var productionOrder = await _recursiveProcessor.ProcessRecursiveProductionAsync(
            productId, quantity, costCenterId);

        productionOrder.PlannedStartDate = plannedStartDate;
        productionOrder.Priority = priority;
        productionOrder.EstimatedCost = validationResult.EstimatedCost;
        productionOrder.PlannedEndDate = validationResult.EstimatedCompletionDate;

        // Update the production order
        await _productionOrderRepository.UpdateAsync(productionOrder);

        _logger.LogInformation("Created production order {OrderNumber} for Product {ProductCode}", 
            productionOrder.OrderNumber, product.ProductCode);

        return productionOrder;
    }

    public async Task<ProductionValidationResult> ValidateProductionOrderAsync(int productionOrderId)
    {
        var productionOrder = await _productionOrderRepository.GetByIdAsync(productionOrderId);
        if (productionOrder == null)
            throw new ArgumentException($"Production order with ID {productionOrderId} not found");

        return await ValidateProductionOrderAsync(
            productionOrder.ProductId, 
            productionOrder.QuantityToProduce, 
            productionOrder.CostCenterId);
    }

    private async Task<ProductionValidationResult> ValidateProductionOrderAsync(
        int productId, 
        decimal quantity, 
        int costCenterId)
    {
        var result = new ProductionValidationResult { IsValid = true };

        try
        {
            // Validate dependencies
            var dependencyResult = await _recursiveProcessor.ValidateDependenciesAsync(productId, quantity, costCenterId);
            if (!dependencyResult.IsValid)
            {
                result.IsValid = false;
                result.Errors.AddRange(dependencyResult.Errors);
                result.DependencyIssues.AddRange(dependencyResult.Issues);
            }

            // Calculate material requirements
            var materialRequirements = await _recursiveProcessor.ResolveMaterialRequirementsRecursivelyAsync(productId, quantity);
            
            // Check material availability
            foreach (var requirement in materialRequirements.Where(r => r.IsRawMaterial))
            {
                var availableQuantity = await _inventoryRepository.GetAvailableQuantityAsync(requirement.ProductId, costCenterId);
                
                if (availableQuantity < requirement.RequiredQuantity)
                {
                    result.IsValid = false;
                    result.MaterialIssues.Add(new MaterialAvailabilityIssue
                    {
                        ProductId = requirement.ProductId,
                        ProductCode = requirement.ProductCode,
                        ProductName = requirement.ProductName,
                        RequiredQuantity = requirement.RequiredQuantity,
                        AvailableQuantity = availableQuantity,
                        Unit = requirement.Unit,
                        CostCenterId = costCenterId,
                        IssueType = "Shortage",
                        Suggestion = $"Purchase or transfer {requirement.RequiredQuantity - availableQuantity} {requirement.Unit}"
                    });
                }
            }

            // Calculate costs and timing
            result.EstimatedCost = materialRequirements.Sum(r => r.TotalCost);
            result.EstimatedDurationMinutes = await _recursiveProcessor.CalculateTotalProductionTimeAsync(productId, quantity);
            result.EarliestStartDate = DateTime.UtcNow;
            result.EstimatedCompletionDate = result.EarliestStartDate.AddMinutes(result.EstimatedDurationMinutes);

            // Add warnings for near-expiry materials
            await CheckForExpiringMaterials(materialRequirements, costCenterId, result);

        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating production order for Product {ProductId}", productId);
            result.IsValid = false;
            result.Errors.Add($"Validation error: {ex.Message}");
        }

        return result;
    }

    public async Task<ProductionOrder> StartProductionAsync(int productionOrderId, int userId)
    {
        _logger.LogInformation("Starting production order {ProductionOrderId} by user {UserId}", productionOrderId, userId);

        var productionOrder = await _productionOrderRepository.GetByIdWithHierarchyAsync(productionOrderId);
        if (productionOrder == null)
            throw new ArgumentException($"Production order with ID {productionOrderId} not found");

        if (productionOrder.Status != ProductionOrderStatus.Planned && productionOrder.Status != ProductionOrderStatus.ReadyToStart)
            throw new InvalidOperationException($"Production order {productionOrder.OrderNumber} cannot be started. Current status: {productionOrder.Status}");

        // Reserve materials
        var reservationResult = await ReserveMaterialsAsync(productionOrderId);
        if (!reservationResult.IsSuccessful)
        {
            var errors = string.Join("; ", reservationResult.Errors);
            throw new InvalidOperationException($"Failed to reserve materials: {errors}");
        }

        // Update status
        productionOrder.Status = ProductionOrderStatus.InProgress;
        productionOrder.ActualStartDate = DateTime.UtcNow;
        productionOrder.ModifiedDate = DateTime.UtcNow;
        productionOrder.ModifiedBy = userId;

        await _productionOrderRepository.UpdateAsync(productionOrder);

        _logger.LogInformation("Started production order {OrderNumber}", productionOrder.OrderNumber);

        return productionOrder;
    }

    public async Task<ProductionOrder> CompleteProductionAsync(int productionOrderId, decimal actualQuantityProduced, int userId)
    {
        _logger.LogInformation("Completing production order {ProductionOrderId} with quantity {Quantity}", 
            productionOrderId, actualQuantityProduced);

        var productionOrder = await _productionOrderRepository.GetByIdWithHierarchyAsync(productionOrderId);
        if (productionOrder == null)
            throw new ArgumentException($"Production order with ID {productionOrderId} not found");

        if (productionOrder.Status != ProductionOrderStatus.InProgress)
            throw new InvalidOperationException($"Production order {productionOrder.OrderNumber} is not in progress");

        // Update production order
        productionOrder.Status = ProductionOrderStatus.Completed;
        productionOrder.QuantityProduced = actualQuantityProduced;
        productionOrder.ActualEndDate = DateTime.UtcNow;
        productionOrder.ModifiedDate = DateTime.UtcNow;
        productionOrder.ModifiedBy = userId;

        // Create inventory receipt for produced items
        var inventoryMovement = new InventoryMovement
        {
            ProductId = productionOrder.ProductId,
            CostCenterId = productionOrder.CostCenterId,
            MovementType = InventoryMovementType.ProductionReceipt,
            ReferenceNumber = productionOrder.OrderNumber,
            Quantity = actualQuantityProduced,
            Unit = productionOrder.Unit,
            UnitCost = productionOrder.EstimatedCost / productionOrder.QuantityToProduce,
            ProductionOrderId = productionOrderId,
            UserId = userId,
            Notes = $"Production completion for order {productionOrder.OrderNumber}"
        };

        await _inventoryRepository.RecordMovementAsync(inventoryMovement);

        // Update inventory levels
        await _inventoryRepository.UpdateInventoryAsync(
            productionOrder.ProductId,
            productionOrder.CostCenterId,
            actualQuantityProduced,
            inventoryMovement.UnitCost);

        await _productionOrderRepository.UpdateAsync(productionOrder);

        _logger.LogInformation("Completed production order {OrderNumber}, produced {Quantity} {Unit}", 
            productionOrder.OrderNumber, actualQuantityProduced, productionOrder.Unit);

        return productionOrder;
    }

    public async Task<ProductionOrder> CancelProductionAsync(int productionOrderId, string reason, int userId)
    {
        _logger.LogInformation("Cancelling production order {ProductionOrderId}, Reason: {Reason}", productionOrderId, reason);

        var productionOrder = await _productionOrderRepository.GetByIdWithHierarchyAsync(productionOrderId);
        if (productionOrder == null)
            throw new ArgumentException($"Production order with ID {productionOrderId} not found");

        if (productionOrder.Status == ProductionOrderStatus.Completed)
            throw new InvalidOperationException($"Cannot cancel completed production order {productionOrder.OrderNumber}");

        // Release reserved materials
        await ReleaseMaterialsAsync(productionOrderId);

        // Cancel child orders
        foreach (var childOrder in productionOrder.ChildProductionOrders)
        {
            if (childOrder.Status != ProductionOrderStatus.Completed && childOrder.Status != ProductionOrderStatus.Cancelled)
            {
                await CancelProductionAsync(childOrder.ProductionOrderId, $"Parent order cancelled: {reason}", userId);
            }
        }

        // Update status
        productionOrder.Status = ProductionOrderStatus.Cancelled;
        productionOrder.Notes = $"Cancelled: {reason}";
        productionOrder.ModifiedDate = DateTime.UtcNow;
        productionOrder.ModifiedBy = userId;

        await _productionOrderRepository.UpdateAsync(productionOrder);

        _logger.LogInformation("Cancelled production order {OrderNumber}", productionOrder.OrderNumber);

        return productionOrder;
    }

    public async Task<ProductionHierarchy> GetProductionHierarchyAsync(int productId, decimal quantity)
    {
        return await BuildProductionHierarchy(productId, quantity, 0);
    }

    public async Task<MaterialRequirements> CalculateMaterialRequirementsAsync(int productId, decimal quantity)
    {
        var product = await _productRepository.GetByIdAsync(productId);
        if (product == null)
            throw new ArgumentException($"Product with ID {productId} not found");

        var requirements = await _recursiveProcessor.ResolveMaterialRequirementsRecursivelyAsync(productId, quantity);

        return new MaterialRequirements
        {
            ProductId = productId,
            ProductCode = product.ProductCode,
            ProductName = product.ProductName,
            Quantity = quantity,
            Requirements = requirements.ToList()
        };
    }

    public async Task<MaterialReservationResult> ReserveMaterialsAsync(int productionOrderId)
    {
        var result = new MaterialReservationResult { IsSuccessful = true };

        try
        {
            var productionOrder = await _productionOrderRepository.GetByIdWithHierarchyAsync(productionOrderId);
            if (productionOrder == null)
                throw new ArgumentException($"Production order with ID {productionOrderId} not found");

            var materialRequirements = await _recursiveProcessor.ResolveMaterialRequirementsRecursivelyAsync(
                productionOrder.ProductId, productionOrder.QuantityToProduce);

            foreach (var requirement in materialRequirements.Where(r => r.IsRawMaterial))
            {
                var reserved = await _inventoryRepository.ReserveInventoryAsync(
                    requirement.ProductId,
                    productionOrder.CostCenterId,
                    requirement.RequiredQuantity,
                    productionOrderId);

                if (!reserved)
                {
                    result.IsSuccessful = false;
                    result.Errors.Add($"Failed to reserve {requirement.RequiredQuantity} {requirement.Unit} of {requirement.ProductCode}");
                }
                else
                {
                    result.Reservations.Add(new MaterialReservation
                    {
                        ProductId = requirement.ProductId,
                        ProductCode = requirement.ProductCode,
                        ReservedQuantity = requirement.RequiredQuantity,
                        Unit = requirement.Unit,
                        CostCenterId = productionOrder.CostCenterId,
                        ReservationDate = DateTime.UtcNow,
                        UnitCost = requirement.UnitCost
                    });
                }
            }

            result.TotalReservedValue = result.Reservations.Sum(r => r.TotalValue);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reserving materials for production order {ProductionOrderId}", productionOrderId);
            result.IsSuccessful = false;
            result.Errors.Add($"Reservation error: {ex.Message}");
        }

        return result;
    }

    public async Task<MaterialReservationResult> ReleaseMaterialsAsync(int productionOrderId)
    {
        var result = new MaterialReservationResult { IsSuccessful = true };

        try
        {
            var productionOrder = await _productionOrderRepository.GetByIdWithHierarchyAsync(productionOrderId);
            if (productionOrder == null)
                throw new ArgumentException($"Production order with ID {productionOrderId} not found");

            var materialRequirements = await _recursiveProcessor.ResolveMaterialRequirementsRecursivelyAsync(
                productionOrder.ProductId, productionOrder.QuantityToProduce);

            foreach (var requirement in materialRequirements.Where(r => r.IsRawMaterial))
            {
                var released = await _inventoryRepository.ReleaseReservedInventoryAsync(
                    requirement.ProductId,
                    productionOrder.CostCenterId,
                    requirement.RequiredQuantity,
                    productionOrderId);

                if (!released)
                {
                    result.Errors.Add($"Failed to release {requirement.RequiredQuantity} {requirement.Unit} of {requirement.ProductCode}");
                }
            }

            result.IsSuccessful = result.Errors.Count == 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error releasing materials for production order {ProductionOrderId}", productionOrderId);
            result.IsSuccessful = false;
            result.Errors.Add($"Release error: {ex.Message}");
        }

        return result;
    }

    public async Task<MaterialConsumption> RecordMaterialConsumptionAsync(
        int productionOrderId, 
        int recipeIngredientId, 
        decimal actualQuantity, 
        int? batchId, 
        int userId)
    {
        // Implementation for recording material consumption
        // This would create MaterialConsumption records and update inventory
        throw new NotImplementedException("Material consumption recording will be implemented in the next phase");
    }

    public async Task<IEnumerable<ProductionOrder>> GetProductionOrdersByStatusAsync(
        ProductionOrderStatus status, 
        int? costCenterId = null)
    {
        return await _productionOrderRepository.GetByStatusAsync(status, costCenterId);
    }

    public async Task<ProductionOrder?> GetProductionOrderWithHierarchyAsync(int productionOrderId)
    {
        return await _productionOrderRepository.GetByIdWithHierarchyAsync(productionOrderId);
    }

    // Private helper methods

    private async Task<ProductionHierarchy> BuildProductionHierarchy(int productId, decimal quantity, int level)
    {
        var product = await _productRepository.GetByIdWithRecipeAsync(productId);
        if (product == null)
            throw new ArgumentException($"Product with ID {productId} not found");

        var hierarchy = new ProductionHierarchy
        {
            ProductId = productId,
            ProductCode = product.ProductCode,
            ProductName = product.ProductName,
            Quantity = quantity,
            Unit = product.UnitName,
            HierarchyLevel = level,
            IsRecipe = product.IsRecipe,
            IsProduction = product.IsProduction,
            EstimatedCost = product.CostPerUnit * quantity
        };

        if (product.Recipe != null)
        {
            hierarchy.EstimatedCost = product.Recipe.TotalRecipeCost * quantity;
            hierarchy.EstimatedDurationMinutes = product.Recipe.EstimatedProductionTimeMinutes;

            foreach (var ingredient in product.Recipe.Ingredients.Where(i => i.IsActive))
            {
                var requiredQuantity = ingredient.RequiredQuantity * quantity;

                if (ingredient.RequiresProduction)
                {
                    var childHierarchy = await BuildProductionHierarchy(
                        ingredient.IngredientProductId, requiredQuantity, level + 1);
                    hierarchy.Children.Add(childHierarchy);
                }
                else
                {
                    hierarchy.DirectMaterials.Add(new MaterialRequirement
                    {
                        ProductId = ingredient.IngredientProductId,
                        ProductCode = ingredient.IngredientProduct.ProductCode,
                        ProductName = ingredient.IngredientProduct.ProductName,
                        RequiredQuantity = requiredQuantity,
                        Unit = ingredient.RequiredUnit,
                        UnitCost = ingredient.CostPerUnit,
                        IsRawMaterial = true,
                        HierarchyLevel = level + 1
                    });
                }
            }
        }

        return hierarchy;
    }

    private async Task CheckForExpiringMaterials(
        IEnumerable<MaterialRequirement> materialRequirements, 
        int costCenterId, 
        ProductionValidationResult result)
    {
        foreach (var requirement in materialRequirements.Where(r => r.IsRawMaterial))
        {
            var expiringBatches = await _inventoryRepository.GetExpiringBatchesAsync(7, costCenterId);
            var productBatches = expiringBatches.Where(b => b.ProductId == requirement.ProductId);

            foreach (var batch in productBatches)
            {
                result.Warnings.Add($"Material {requirement.ProductCode} batch {batch.BatchNumber} expires on {batch.ExpirationDate:yyyy-MM-dd}");
            }
        }
    }
}
